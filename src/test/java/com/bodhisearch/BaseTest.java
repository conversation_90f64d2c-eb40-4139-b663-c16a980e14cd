package com.bodhisearch;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.Base64;
import java.util.List;
import java.util.stream.Collectors;

import static org.hamcrest.Matchers.notNullValue;
import static org.hamcrest.Matchers.nullValue;
import org.junit.jupiter.api.BeforeAll;
import org.keycloak.TokenVerifier;
import org.keycloak.admin.client.Keycloak;
import org.keycloak.admin.client.resource.RealmResource;
import org.keycloak.common.VerificationException;
import org.keycloak.jose.jws.JWSInput;
import org.keycloak.jose.jws.JWSInputException;
import org.keycloak.representations.AccessToken;
import org.keycloak.representations.idm.KeysMetadataRepresentation.KeyMetadataRepresentation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.testcontainers.junit.jupiter.Container;
import org.testcontainers.junit.jupiter.Testcontainers;

import com.bodhisearch.templates.RealmConfigGenerator;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import dasniko.testcontainers.keycloak.KeycloakContainer;
import io.restassured.RestAssured;
import io.restassured.http.ContentType;
import io.restassured.path.json.JsonPath;
import io.restassured.response.Response;
import io.restassured.specification.RequestSpecification;

@Testcontainers
public class BaseTest {
  public static final Logger LOGGER = LoggerFactory.getLogger(BaseTest.class);

  public static final String REALM = "bodhi";
  public static final String PROVIDER_ID = "bodhi";
  public static final String RESOURCE_ABCD = "resource-abcd";
  public static final String RESOURCE_SECRET = "change-me";
  public static final String RESOURCE_WXYZ = "resource-wxyz";
  public static final String CLIENT_LMNO = "client-lmno";
  public static final String CLIENT_SECRET = "change-me";
  public static final String RESOURCE_EMPTY = "resource-empty";
  public static final String ROLE_RESOURCE_ADMIN = "resource_admin";
  public static final String ROLE_RESOURCE_MANAGER = "resource_manager";
  public static final String ROLE_RESOURCE_POWER_USER = "resource_power_user";
  public static final String ROLE_RESOURCE_USER = "resource_user";
  protected static String tokenUrl;
  protected static String registerClientUrl;
  protected static Keycloak admin;
  protected static RealmResource realm;
  protected static String resourceAdminUrl;
  protected static String addToGroupUrl;
  protected static String hasAdminUrl;

  @Container
  public static final KeycloakContainer keycloak = new KeycloakContainer("quay.io/keycloak/keycloak:26.1.5")
      .withFeaturesEnabled("admin-fine-grained-authz", "token-exchange")
      .withProviderClassesFrom("target/classes")
      .withEnv("APP_ENV", "test")
  // .withDebugFixedPort(8787, true)
  ;

  @BeforeAll
  public static void importConfigs() {
    String filename = "./src/test/resources/import-files/bodhi-realm-generated.json";
    RealmConfigGenerator.generate(filename);
    importFile(keycloak.getAuthServerUrl(), keycloak.getAdminUsername(),
        keycloak.getAdminPassword(),
        filename);
    tokenUrl = String.format("%s/realms/%s/protocol/openid-connect/token", keycloak.getAuthServerUrl(), REALM);
    registerClientUrl = String.format("%s/realms/%s/bodhi/clients", keycloak.getAuthServerUrl(), REALM);
    admin = keycloak.getKeycloakAdminClient();
    realm = admin.realm(REALM);

    resourceAdminUrl = buildResourceAdminUrl(REALM, PROVIDER_ID);
    addToGroupUrl = buildAddToGroupUrl(REALM, PROVIDER_ID);
    hasAdminUrl = buildHasAdminUrl(REALM, PROVIDER_ID);
  }

  public static RequestSpecification given() {
    return RestAssured.given().relaxedHTTPSValidation().redirects().follow(false)
    // .filter(new RequestLoggingFilter(LogDetail.ALL))
    // .filter(new ResponseLoggingFilter(LogDetail.ALL))
    ;
  }

  public static void importFile(String keycloakUrl, String username, String password, String filename) {
    try {
      String[] command = { "java", "-jar",
          // "tools/keycloak-config-cli-24.0.5.jar",
          "tools/keycloak-config-cli-26.1.0.jar",
          "--import.files.locations=" + filename,
          "--keycloak.url=" + keycloakUrl,
          "--keycloak.user=" + username,
          "--keycloak.password=" + password,
      };
      Process process = Runtime.getRuntime().exec(command);
      BufferedReader stdoutReader = new BufferedReader(new InputStreamReader(process.getInputStream()));
      BufferedReader stderrReader = new BufferedReader(new InputStreamReader(process.getErrorStream()));
      String line;

      while ((line = stdoutReader.readLine()) != null) {
        LOGGER.info(line);
      }

      while ((line = stderrReader.readLine()) != null) {
        LOGGER.error(line);
      }
      process.waitFor();

      int exitCode = process.exitValue();
      if (exitCode == 0) {
        System.out.println("CLI executed successfully.");
      } else {
        String errMsg = "CLI execution failed with exit code: " + exitCode;
        System.out.println(errMsg);
        throw new RuntimeException(errMsg);
      }
    } catch (IOException | InterruptedException e) {
      e.printStackTrace();
      throw new RuntimeException(e);
    }
  }

  public String[] refreshTokenFlow(String clientId, String clientSecret, String tokenUrl, String refreshToken) {
    Response response = given()
        .contentType(ContentType.URLENC)
        .formParam("grant_type", "refresh_token")
        .formParam("client_id", clientId)
        .formParam("client_secret", clientSecret)
        .formParam("refresh_token", refreshToken)
        .when()
        .post(tokenUrl);
    if (response.getStatusCode() != 200) {
      System.out.println("Error response: " + response.asString());
      throw new RuntimeException(String.format("Failed to obtain token. Status code: %d, Message: %s",
          response.getStatusCode(), response.asString()));
    }
    return new String[] { response.jsonPath().getString("access_token"),
        response.jsonPath().getString("refresh_token") };
  }

  public static Response getUserTokenResponseWith(String clientId, String clientSecret, String tokenUrl,
      String username,
      String password, List<String> scopes) {
    RequestSpecification request = given()
        .relaxedHTTPSValidation()
        .redirects().follow(false)
        .contentType(ContentType.URLENC)
        .formParam("grant_type", "password")
        .formParam("client_id", clientId)
        .formParam("client_secret", clientSecret)
        .formParam("username", username)
        .formParam("password", password)
        .formParam("scope", scopes.stream().collect(Collectors.joining(" ")));
    Response response = request
        .when()
        .post(tokenUrl);
    return response;
  }

  public static String getUserTokenWith(String clientId, String clientSecret, String tokenUrl, String username,
      String password) {
    return getUserTokenWith(clientId, clientSecret, tokenUrl, username, password, Arrays.asList("openid"));
  }

  public static String getUserTokenWith(String clientId, String clientSecret, String tokenUrl, String username,
      String password, List<String> scopes) {
    Response response = getUserTokenResponseWith(clientId, clientSecret, tokenUrl, username, password, scopes);

    if (response.getStatusCode() != 200) {
      System.out.println("Error response: " + response.asString());
      throw new RuntimeException(String.format("Failed to obtain token. Status code: %d, Message: %s",
          response.getStatusCode(), response.asString()));
    }
    return response.jsonPath().getString("access_token");
  }

  public static String[] getUserTokenPairWith(String clientId, String clientSecret, String tokenUrl, String username,
      String password, List<String> scopes) {
    Response response = getUserTokenResponseWith(clientId, clientSecret, tokenUrl, username, password, scopes);

    if (response.getStatusCode() != 200) {
      System.out.println("Error response: " + response.asString());
      throw new RuntimeException(String.format("Failed to obtain token. Status code: %d, Message: %s",
          response.getStatusCode(), response.asString()));
    }
    String[] tokenPair = new String[] { response.jsonPath().getString("access_token"),
        response.jsonPath().getString("refresh_token") };
    return tokenPair;
  }

  protected static String getTokenForClient(String clientId, String clientSecret) {
    Response response = given()
        .contentType(ContentType.URLENC)
        .formParam("grant_type", "client_credentials")
        .formParam("client_id", clientId)
        .formParam("client_secret", clientSecret)
        .when()
        .post(tokenUrl);
    return response
        .then()
        .statusCode(200)
        .contentType(ContentType.JSON)
        .body("error", nullValue())
        .body("access_token", notNullValue())
        .extract()
        .jsonPath().getString("access_token");
  }

  protected String[] exchangeToOfflineToken(String clientId, String clientSecret, String subjectToken,
      List<String> scopes) {
    JsonPath response = given()
        .contentType(ContentType.URLENC)
        .formParam("grant_type", "urn:ietf:params:oauth:grant-type:token-exchange")
        .formParam("client_id", clientId)
        .formParam("client_secret", clientSecret)
        .formParam("subject_token", subjectToken)
        .formParam("requested_token_type", "urn:ietf:params:oauth:token-type:refresh_token")
        .formParam("scope", scopes.stream().collect(Collectors.joining(" ")))
        .post(tokenUrl)
        .then()
        .statusCode(200)
        .contentType(ContentType.JSON)
        .body("error", nullValue())
        .body("access_token", notNullValue())
        .extract()
        .jsonPath();
    return new String[] { response.getString("access_token"), response.getString("refresh_token") };
  }

  protected String exchangeClientResourceToken(String clientId, String sourceToken, String audience,
      String accessToken) {
    return exchangeToken(clientId, sourceToken, audience, accessToken)
        .then()
        .contentType(ContentType.JSON)
        .body("error", nullValue())
        .statusCode(200)
        .body("access_token", notNullValue())
        .extract().jsonPath()
        .getString("access_token");
  }

  protected String offlineToken(String clientId, String sourceToken, String audience, String accessToken) {
    return exchangeToken(clientId, sourceToken, audience, accessToken)
        .then()
        .contentType(ContentType.JSON)
        .body("error", nullValue())
        .statusCode(200)
        .body("access_token", notNullValue())
        .extract().jsonPath()
        .getString("access_token");
  }

  protected Response exchangeToken(String clientId, String userClientAccessToken, String resource,
      String resourceTokenAsBearer) {
    RequestSpecification request = given()
        .contentType(ContentType.URLENC)
        .formParam("grant_type", "urn:ietf:params:oauth:grant-type:token-exchange")
        .formParam("subject_token", userClientAccessToken)
        .formParam("client_id", clientId)
        .formParam("audience", resource)
        .formParam("scope", "openid email profile roles");
    if (resourceTokenAsBearer != null) {
      request.header("Authorization", resourceTokenAsBearer);
    }
    return request
        .when()
        .post(tokenUrl);
  }

  protected static String getResourceToken() {
    return getTokenForClient(RESOURCE_ABCD, RESOURCE_SECRET);
  }

  protected static String getResourceTokenMakeFirstAdmin() {
    return getTokenForClient("resource-make-first-admin", "change-me");
  }

  protected String getOtherResourceToken() {
    return getTokenForClient(RESOURCE_WXYZ, RESOURCE_SECRET);
  }

  protected String getEmptyResourceToken() {
    return getTokenForClient(RESOURCE_EMPTY, RESOURCE_SECRET);
  }

  protected String getOtherUserToken() {
    return getUserTokenWith(RESOURCE_ABCD, RESOURCE_SECRET, tokenUrl, "<EMAIL>", "pass");
  }

  protected String getOtherAdminToken() {
    return getUserTokenWith(RESOURCE_ABCD, RESOURCE_SECRET, tokenUrl, "<EMAIL>", "pass");
  }

  protected String getResourceAdminUserToken() {
    return getUserTokenWith(RESOURCE_ABCD, RESOURCE_SECRET, tokenUrl, "<EMAIL>", "pass");
  }

  protected JWSInput getJws(String accessToken) {
    try {
      return new JWSInput(accessToken);
    } catch (JWSInputException e) {
      throw new RuntimeException(e);
    }
  }

  protected AccessToken decodeToken(String token) {
    try {
      TokenVerifier<AccessToken> verifier = TokenVerifier.create(token, AccessToken.class);
      return verifier.parse().getToken();
    } catch (VerificationException e) {
      throw new RuntimeException("Failed to decode token", e);
    }
  }

  protected KeyMetadataRepresentation getKeyRepr(String userToken)
      throws JsonProcessingException, JsonMappingException {
    String header = userToken.split("\\.")[0];
    byte[] decodedBytes = Base64.getDecoder().decode(header);
    String decodedHeader = new String(decodedBytes, StandardCharsets.UTF_8);
    ObjectMapper objectMapper = new ObjectMapper();
    JsonNode jsonHeader = objectMapper.readTree(decodedHeader);
    String kid = jsonHeader.get("kid").asText();
    KeyMetadataRepresentation keyRepr = realm.keys().getKeyMetadata().getKeys().stream()
        .filter(k -> k.getKid().equals(kid))
        .findFirst().get();
    return keyRepr;
  }

  protected JsonPath registerClient() {
    JsonPath response = given()
        .contentType(ContentType.JSON)
        .body("{\"redirect_uris\": [\"http://bodhiapp.localhost/app/callback\"]}")
        .when()
        .post(registerClientUrl)
        .then()
        .body("error", nullValue())
        .statusCode(201)
        .body("client_id", notNullValue())
        .body("client_secret", notNullValue())
        .extract()
        .jsonPath();
    return response;
  }

  protected String[] registerClientAndReturnTokenPair() {
    JsonPath response = registerClient();
    return new String[] { response.getString("client_id"), response.getString("client_secret") };
  }

  protected Response makeResourceAdmin(String resourceAdminUrl, String token, String userToMakeFirstAdmin) {
    return given()
        .header("Authorization", "Bearer " + token)
        .contentType(ContentType.JSON)
        .body(String.format("{\"username\": \"%s\"}", userToMakeFirstAdmin))
        .when()
        .post(resourceAdminUrl);
  }

  protected static String buildResourceAdminUrl(String realm, String providerId) {
    return String.format(
        "%s/realms/%s/%s/clients/make-resource-admin",
        keycloak.getAuthServerUrl(),
        realm,
        providerId);
  }

  protected static String buildAddToGroupUrl(String realm, String providerId) {
    return String.format(
        "%s/realms/%s/%s/clients/add-user-to-group",
        keycloak.getAuthServerUrl(),
        realm,
        providerId);
  }

  protected static String buildHasAdminUrl(String realm, String providerId) {
    return String.format(
        "%s/realms/%s/%s/clients/has-resource-admin",
        keycloak.getAuthServerUrl(),
        realm,
        providerId);
  }

  protected Response addUserToGroup(String url, String token, String testUser, String group) {
    return given()
        .header("Authorization", "Bearer " + token)
        .contentType(ContentType.JSON)
        .body(String.format("{\"username\": \"%s\", \"group\": \"%s\", \"add\": true}", testUser, group))
        .when()
        .post(url);
  }
}
